import axios from "axios";
import { EventEmitter } from "events";
import * as SecureStore from "expo-secure-store";
import Constants from "expo-constants";

export const eventEmitter = new EventEmitter();

const instance = axios.create({
  // baseURL: `http://192.168.1.64:3008/api/v1`,
  baseURL: `${Constants.expoConfig?.extra?.apiUrl}/api/v1`,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 20000,
});

instance.interceptors.request.use(
  async (config) => {
    const authToken = await SecureStore.getItemAsync("authToken");
    if (authToken && config.headers) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle 401 errors.
instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log("Response error:", error);
    console.log(error?.response?.status);
    console.log(error?.response?.data);
    if (
      error?.response?.data?.error?.includes("Network") &&
      error?.response?.data?.error?.includes("not found")
    ) {
      alert("Your account is not set up. Please reach out to support.");
    }
    if (error.response && error?.response?.status === 401) {
      console.log("Unauthorized, logging out...");
      // eventEmitter.emit("logout");
      await SecureStore.deleteItemAsync("authToken");
    }
    return Promise.reject(error);
  }
);

export default instance;
