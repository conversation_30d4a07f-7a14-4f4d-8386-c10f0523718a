import { useQuery } from "@tanstack/react-query";
import axios from "../services/axiosConfig";

export interface Facility {
  id: string;
  name: string;
}

export const useFacilities = () => {
  return useQuery<Facility[], Error>({
    queryKey: ["facilities"],
    queryFn: async () => {
      const response = await axios.get("/user/facilities");
      return response.data.facilities;
    },
    staleTime: 10 * 60 * 1000, // Data is fresh for 10 minutes.
  });
};
