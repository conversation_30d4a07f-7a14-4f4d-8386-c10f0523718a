import React, { useEffect } from "react";
import {
  View,
  Platform,
  Modal,
  Vibration,
  NativeModules,
  Alert,
} from "react-native";
import CallKitService from "~/services/CallKitService";
import { YStack, XStack, Text, Button, Dialog } from "tamagui";
import { Phone, CircleX } from "@tamagui/lucide-icons";
import { displayDate } from "src/utils/utils";
import { useRouter } from "expo-router";
import { useSocket } from "~/context/ProviderSocketContext";
import axiosConfig from "~/services/axiosConfig";
import { useConsultationRequest } from "~/context/ConsultationRequestContext"; // Adjust path

interface ConsultationRequestData {
  consultationRequestId: string;
  facilityId: string;
  patientName: string;
  patientDOB: string;
  patientGender: string;
  location: string;
  caller: string;
  chief_complaint: string;
}

interface TimedConsultationRequestData extends ConsultationRequestData {
  timeLeft: number;
}

const ProviderSocketListener: React.FC = () => {
  const styles = useCallRequestDialogStyle();
  const { socket } = useSocket();
  const router = useRouter();
  const { requestQueue, setRequestQueue } = useConsultationRequest(); // Use context
  const currentRequest = requestQueue.length > 0 ? requestQueue[0] : null;

  // see if any consultations are pending
  useEffect(() => {
    // call axios to get consultations
    const fetchConsultations = async () => {
      try {
        const response = await axiosConfig.get("/consultation/pending");
        if (response.status === 200) {
          if (response.data.length === 0) {
            console.log("No pending consultations");
            return;
          }
          setRequestQueue((prevQueue) => [
            ...prevQueue,
            ...response.data.map((item: ConsultationRequestData) => ({
              ...item,
              timeLeft: 30, // Set initial time left for each request
            })),
          ]);
        }
        console.log("Pending consultations:", response.data);
      } catch (error) {
        console.error("Error fetching consultations", error);
      }
    };
    fetchConsultations();
  }, []);

  // Update timers in the requestQueue
  useEffect(() => {
    // Only set up the interval if there is at least one request.
    if (requestQueue.length === 0) return;

    const interval = setInterval(() => {
      try {
        setRequestQueue((prevQueue) => {
          // Check if the queue is empty to avoid unnecessary updates
          if (prevQueue.length === 0) {
            return prevQueue;
          }

          // Update timeLeft for each request and filter out expired ones
          const updatedQueue = prevQueue
            .map((req) => ({ ...req, timeLeft: req.timeLeft - 1 }))
            .filter((req) => req.timeLeft > 0);

          // If all requests have expired, cancel vibration and return an empty array
          if (updatedQueue.length === 0) {
            console.log("All requests have expired - stopping vibration");
            // Cancel vibration when all requests expire
            Vibration.cancel();

            // End any active CallKit calls
            if (Platform.OS === "ios") {
              CallKitService.endAllCalls()
                .then(() =>
                  console.log(
                    "Ended all CallKit calls after request expiration"
                  )
                )
                .catch((err) =>
                  console.error("Error ending CallKit calls:", err)
                );
            }
          }

          return updatedQueue;
        });
      } catch (error) {
        console.error("Error updating request timers:", error);
        // Clear the interval in case of error
        clearInterval(interval);
        // Make sure to cancel vibration in case of error
        Vibration.cancel();
      }
    }, 1000);

    return () => {
      // Make sure to clear the interval on cleanup
      clearInterval(interval);
    };
  }, [requestQueue.length, setRequestQueue]);

  // Socket listener for incoming consultation requests
  useEffect(() => {
    if (!socket) return;
    socket.on("consultationRequest", (data: ConsultationRequestData) => {
      setRequestQueue((prevQueue) => [...prevQueue, { ...data, timeLeft: 30 }]);
    });
    return () => {
      socket.off("consultationRequest");
    };
  }, [socket, setRequestQueue]);

  useEffect(() => {
    if (!socket) return;
    socket.on(
      "consultationAcceptedByOther",
      (data: { consultationRequestId: string }) => {
        const consultationRequestId = data.consultationRequestId;

        setRequestQueue((prevQueue) => {
          if (prevQueue.length === 0) {
            return prevQueue;
          }

          const currentRequestId = prevQueue[0].consultationRequestId;

          if (currentRequestId === consultationRequestId) {
            // Force the modal to close by setting the queue to empty
            setTimeout(() => {
              setRequestQueue([]);
            }, 0);
            return [];
          }
          return prevQueue;
        });
      }
    );

    return () => {
      socket.off("consultationAcceptedByOther");
    };
  }, [socket, setRequestQueue, currentRequest]);

  // New socket listener to remove requests that are accepted by another provider
  useEffect(() => {
    if (!socket) return;
    socket.on(
      "consultationAccepted",
      (data: { consultationId: string; consultationRequestId: string }) => {
        setRequestQueue((prevQueue) =>
          prevQueue.filter(
            (req) => req.consultationRequestId !== data.consultationRequestId
          )
        );
      }
    );
    return () => {
      socket.off("consultationAccepted");
    };
  }, [socket, setRequestQueue]);

  const handleDeclineRequest = async () => {
    try {
      // Cancel any ongoing vibration
      Vibration.cancel();
      console.log("Canceled vibration in handleDeclineRequest");

      // End any active CallKit calls first
      if (Platform.OS === "ios") {
        try {
          // Get active calls for debugging
          const activeCalls = await CallKitService.getActiveCalls();
          console.log("Active calls before ending:", activeCalls);

          // Use our enhanced CallKitService to end all calls
          const result = await CallKitService.endAllCalls();
          console.log("Ended all active CallKit calls:", result);

          // If there are still active calls, use the nuclear option
          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log("Remaining calls after ending:", remainingCalls);

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              // Try to directly end calls using the native module
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }

              // Also try the nuclear option
              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error("Error ending CallKit calls:", error);
        }
      }

      // Clear all requests from the queue instead of just the first one
      console.log("Clearing all requests from the queue");
      setRequestQueue([]);
    } catch (error) {
      console.error("Error declining request:", error);
      // Ensure the queue is reset to a safe state
      setRequestQueue([]);
      // Make sure vibration is canceled even if there's an error
      Vibration.cancel();
    }
  };

  const handleAcceptRequest = async () => {
    if (!currentRequest) return;
    try {
      // Cancel any ongoing vibration immediately
      Vibration.cancel();
      console.log("Canceled vibration in handleAcceptRequest");

      // End any active CallKit calls
      if (Platform.OS === "ios") {
        try {
          // Get active calls for debugging
          const activeCalls = await CallKitService.getActiveCalls();
          console.log("Active calls before ending:", activeCalls);

          // Use our enhanced CallKitService to end all calls
          const result = await CallKitService.endAllCalls();
          console.log("Ended all active CallKit calls:", result);

          // If there are still active calls, use the nuclear option
          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log("Remaining calls after ending:", remainingCalls);

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              // Try to directly end calls using the native module
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }

              // Also try the nuclear option
              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error("Error ending CallKit calls:", error);
        }
      }

      const response = await axiosConfig.post("/consultation/accept", {
        consultationRequestId: currentRequest.consultationRequestId,
      });
      console.log("Response from accept request:", response.status);

      if (response.status === 409) {
        console.log("Consultation already accepted by another provider");
        Alert.alert(
          "Consultation already accepted",
          "This consultation has already been accepted by another provider.",
          [{ text: "OK" }]
        );
        // Clear the request queue
        setRequestQueue((prevQueue) =>
          prevQueue.filter(
            (req) =>
              req.consultationRequestId !== currentRequest.consultationRequestId
          )
        );
        return;
      }

      const consultationId = response.data.consultationId;

      // Clear the request queue
      setRequestQueue([]);

      // Double-check that vibration is canceled before navigation
      Vibration.cancel();

      router.push({
        pathname: "/provider/CallDetails",
        params: {
          consultationId,
          isComingFromCall: String(true),
          shouldNotgoBack: String(true),
        },
      });
    } catch (error: any) {
      // if 409, show alert
      if (error.response && error.response.status === 409) {
        Alert.alert(
          "Consultation not available",
          "This consultation has either been accepted or cancelled.",
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Error",
          "An error occurred while accepting the consultation request.",
          [{ text: "OK" }]
        );
      }
      setRequestQueue((prevQueue) =>
        prevQueue.filter(
          (req) =>
            req.consultationRequestId !== currentRequest.consultationRequestId
        )
      );
      // Make sure vibration is canceled even if there's an error
      Vibration.cancel();
    }
  };

  const renderInfoBlock = (label: string, value: string) => (
    <YStack marginBlockStart={20}>
      <Text {...styles.infoBlockLabel}>{label}:</Text>
      <Text {...styles.infoBlockValue}>{value}</Text>
    </YStack>
  );

  if (!currentRequest) {
    return null;
  }

  return (
    <Modal
      visible={!!currentRequest}
      transparent
      animationType="fade"
      onRequestClose={handleDeclineRequest}
    >
      <View
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          height: "100%",
          width: "100%",
        }}
      >
        <Dialog
          modal
          open={!!currentRequest}
          onOpenChange={handleDeclineRequest}
        >
          <Dialog.Overlay {...styles.overlay} />
          <Dialog.Content {...styles.dialogContent}>
            <YStack {...styles.container}>
              {currentRequest && (
                <>
                  <XStack {...styles.headerContainer}>
                    <Text {...styles.headerText}>Call Request</Text>
                    <Text {...styles.callEndsText}>
                      Call ends: {currentRequest.timeLeft}
                    </Text>
                  </XStack>
                  <YStack style={{ maxHeight: 400, overflow: "scroll" }}>
                    {renderInfoBlock("Caller", currentRequest.caller)}
                    {renderInfoBlock("Location", currentRequest.location)}
                    {renderInfoBlock(
                      "Patient",
                      `${currentRequest.patientName}, ${currentRequest.patientGender} | DOB: ${displayDate(currentRequest.patientDOB)}`
                    )}
                    {renderInfoBlock(
                      "Chief Complaint",
                      currentRequest.chief_complaint
                    )}
                  </YStack>
                  <Button
                    {...styles.button}
                    icon={<Phone size={"$1"} />}
                    onPress={handleAcceptRequest}
                  >
                    Accept Call
                  </Button>
                  <Button
                    {...styles.button}
                    icon={<CircleX size={"$1"} />}
                    onPress={handleDeclineRequest}
                  >
                    Decline Request
                  </Button>
                </>
              )}
            </YStack>
          </Dialog.Content>
        </Dialog>
      </View>
    </Modal>
  );
};
// Styles remain unchanged
const useCallRequestDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4",
      shadowColor: "transparent" as any,
      shadowOpacity: 0,
      shadowRadius: 0,
      marginInline: 50,
      width: "90%" as any,
      alignSelf: "center",
      borderRadius: "$7",
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 18,
      fontWeight: "300" as any,
      marginBlockStart: 10,
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
    },
  };
};

export default ProviderSocketListener;
