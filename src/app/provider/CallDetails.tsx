import ScreenHeader from "src/components/ScreenHeader";
import { <PERSON><PERSON>, ScrollView, View, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "tamagui";
import { useCallDetailStyles } from "./Styles/CallDetailStyle";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import CallerNotes from "src/components/calldetailcomponents/CallerNotes";
import Consultation from "src/components/Consultation";
import DropdownTextEditor from "src/components/DropdownTextEditor";
import ProblemListDropDown from "./ProblemListDropdown";
import DemographicsDropDown from "src/components/DemographicsDropDown";
import { Check, Video } from "@tamagui/lucide-icons";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { BackHandler, Modal, Vibration } from "react-native";
import ReviewCallHeader from "src/components/ReviewCallScreenHeader";
import { DialogBox } from "src/components/Dialog";
import axiosConfig from "~/services/axiosConfig";
import { HorizontalDashedLine } from "src/components/DashedLine";
import {
  ArrowUpDown,
  // Barbell,
  Weight,
  Ruler,
  MoveVertical,
  Wind,
  Heart,
  Thermometer,
  Frown,
  Activity,
} from "lucide-react-native";
import PhysicalExam from "src/components/PhysicalExam";
import DropdownDetails from "src/components/DropdownDetails";
import { setShouldRefetchConsultations } from "src/globals/consultationFlag";

type PhysicalExam = {
  [category: string]: {
    [item: string]: string | string[];
  };
};

export default function CallDetails({ goToCall }: { goToCall: () => void }) {
  const callDetailStyle = useCallDetailStyles();
  const router = useRouter();
  const params = useLocalSearchParams();
  const consultationId = params.consultationId as string;
  const isComingFromCall = params.isComingFromCall === "true";
  const [dialogOpen, setDialogOpen] = useState(false);
  const [patientDetails, setPatientDetails] = useState<any>(null);
  const [physicalExam, setPhysicalExam] = useState<PhysicalExam | null>(null);
  // Local state replaces context data.
  const [order, setOrder] = useState("");
  const [callerNotes, setCallerNotes] = useState("");
  const [callEnded, setCallEnded] = useState(false);
  const [selectedCodes, setSelectedCodes] = useState<
    { code: string; description: string }[]
  >([]);

  const { consultation, loading, error } = useConsultationV2(consultationId);
  // Update local state when consultation loads.
  useEffect(() => {
    if (consultation) {
      setOrder(consultation.order || "");
      setCallerNotes(consultation.notes || "");
      setPatientDetails(consultation.patient_details_snapshot || {});
      setPhysicalExam(consultation?.physical_exam);
    }
  }, [consultation]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => true
    );
    return () => backHandler.remove();
  }, []);

  if (loading) {
    return (
      <View {...callDetailStyle.spinner}>
        <Spinner size="large" />
      </View>
    );
  }

  if (error) {
    return (
      <View {...callDetailStyle.container}>
        <YStack {...callDetailStyle.mainStack}>
          <ScreenHeader
            onAvatarPress={() => {}}
            screenName={isComingFromCall ? "Dashboard" : "Back to Call"}
            onBackPress={() => router.back()}
          />
          <View>
            <Text>Error: {error.message}</Text>
          </View>
        </YStack>
      </View>
    );
  }

  // Format date and time based on consultation.created_at.
  const dateObj = consultation?.created_at
    ? new Date(consultation.created_at)
    : new Date();
  const isValidDate = !isNaN(dateObj.getTime());
  const formattedDate = isValidDate
    ? dateObj.toLocaleDateString("en-CA", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      })
    : "";
  const formattedTime = isValidDate
    ? dateObj.toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      })
    : "";

  const patientFullName =
    `${patientDetails?.firstName || ""} ${patientDetails?.lastName || ""}`.trim();

  const acceptCall = () => {
    // Cancel any ongoing vibration
    Vibration.cancel();
    console.log("Canceled vibration in CallDetails acceptCall");

    setCallEnded(false);
    router.replace({
      pathname: "/provider/CallContainer",
      params: { consultationId },
    });
  };

  const completeConsultationCall = async () => {
    await axiosConfig.put(`/consultation/${consultationId}`, {
      order,
      notes: callerNotes,
      patientDetails: patientDetails,
      physical_exam: physicalExam,
      icdCodes: selectedCodes,
    });
  };

  const submitDetails = async () => {
    if (!order.trim()) {
      setDialogOpen(true);
      return;
    }
    await completeConsultationCall();
    setDialogOpen(false);
    console.log("Completing visit...");
    setShouldRefetchConsultations(true);
    router.dismissAll();
    router.push({
      pathname: "/provider/reviewcall",
      params: { consultationId },
    });
  };
  const vitals = consultation?.patient_details_snapshot?.vitals || {};

  const renderVitalBoxes = () => {
    const order = [
      "heartrate",
      "bloodpressure",
      "temperature",
      "respirations",
      "painlevel",
    ];

    const sortedEntries = Object.entries(vitals).sort(([a], [b]) => {
      const aKey = a.toLowerCase();
      const bKey = b.toLowerCase();
      const ia = order.indexOf(aKey);
      const ib = order.indexOf(bKey);
      const ra = ia >= 0 ? ia : order.length;
      const rb = ib >= 0 ? ib : order.length;
      return ra - rb;
    });

    return sortedEntries.map(([key, m]: any) => {
      const label =
        key.toLowerCase() === "bloodpressure"
          ? "Blood Pressure"
          : key.charAt(0).toUpperCase() + key.slice(1);

      const recorded = m.recorded;
      let Icon: React.ComponentType<any> = Activity;
      switch (key) {
        case "weight":
          Icon = Weight;
          break;
        case "height":
          Icon = Ruler;
          break;
        case "heartrate":
          Icon = Heart;
          break;
        case "temperature":
          Icon = Thermometer;
          break;
        case "respirations":
          Icon = Wind;
          break;
        case "painLevel":
        case "painlevel":
          Icon = Frown;
          break;
        case "bloodpressure":
          Icon = ArrowUpDown;
          break;
      }

      const valueText =
        key.toLowerCase() === "bloodpressure"
          ? `${m.systolic}/${m.diastolic}${m.unit ? " " + m.unit : ""}`
          : `${m.value}${m.unit ? " " + m.unit : ""}`;

      return (
        <YStack key={key}>
          <YStack key={key} {...callDetailStyle.vitalBoxContainer}>
            <Text verticalAlign={"center"} fontSize={10} mb={6}>
              {label}
            </Text>
            <Icon size={24} color="black" />
            <Text
              fontSize={12}
              fontWeight="500"
              verticalAlign={"center"}
              color="$confirmOrderTextColor"
              style={{
                width: "100%",
                textAlign: "center",
              }}
            >
              {valueText}
            </Text>
          </YStack>
          {(() => {
            const [datePart, ...rest] = recorded.split(" ");
            const timePart = rest.join(" ");
            return (
              <Text {...callDetailStyle.dateAndTimeText}>
                {datePart}
                {"\n"}
                {timePart}
              </Text>
            );
          })()}
        </YStack>
      );
    });
  };

  return (
    <View {...callDetailStyle.container}>
      <YStack {...callDetailStyle.mainStack}>
        {!callEnded ? (
          <ReviewCallHeader
            screenName={
              isComingFromCall ? "Review Patient Details" : "Back to Call"
            }
            onBackPress={() => goToCall()}
            isFromCallScreen={!isComingFromCall}
          />
        ) : (
          <ReviewCallHeader
            screenName={patientFullName}
            isFromCallScreen={!isComingFromCall}
          />
        )}
        <ScrollView showsVerticalScrollIndicator={false}>
          <YStack {...callDetailStyle.componentStack}>
            {!isComingFromCall && (
              <YStack>
                <CallerNotes
                  value={callerNotes}
                  onChangeText={setCallerNotes}
                />
              </YStack>
            )}
            <YStack {...callDetailStyle.ConsultationData}>
              <Consultation
                data={consultation}
                isFromProvider={true}
                isFromCallScreen={isComingFromCall}
                shouldSowOrderandBadge={false}
              />
            </YStack>
            <YStack>
              {!isComingFromCall && (
                <DropdownTextEditor
                  title="Orders"
                  data={order}
                  onChangeText={setOrder}
                />
              )}
              <HorizontalDashedLine
                height={1}
                dashLength={2}
                dashGap={2}
                color="#D2D6DB"
                style={{ marginTop: 18 }}
              />
            </YStack>
            <YStack>
              {Object.keys(vitals).length > 0 && (
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <YStack flexDirection="row" gap="$3" mt="$4">
                    {renderVitalBoxes()}
                  </YStack>
                </ScrollView>
              )}
            </YStack>
            <YStack>
              <DemographicsDropDown
                title="Demographics"
                data={patientDetails}
              />
            </YStack>
            <YStack>
              <DropdownDetails
                title="Medical History"
                data={patientDetails?.medicalHistory || ""}
              />
            </YStack>
            <YStack>
              <DropdownDetails
                title="Medications"
                data={patientDetails?.medications || ""}
              />
            </YStack>
            <YStack>
              <DropdownDetails
                title="Allergies"
                data={patientDetails?.allergies || ""}
              />
            </YStack>
            {!isComingFromCall && (
              <YStack>
                <ProblemListDropDown
                  title="Problem List"
                  selectedCodes={selectedCodes}
                  setSelectedCodes={setSelectedCodes}
                />
              </YStack>
            )}
            {!isComingFromCall && (
              <YStack>
                <PhysicalExam
                  physicalExam={physicalExam}
                  setPhysicalExam={setPhysicalExam}
                  isSubmitted={false}
                />
              </YStack>
            )}
            <YStack {...callDetailStyle.btnContainer}>
              {isComingFromCall ? (
                <Button
                  {...callDetailStyle.completeVisitBtn}
                  icon={<Video size={"$2"} />}
                  onPress={acceptCall}
                >
                  Start Call
                </Button>
              ) : (
                <Button
                  {...callDetailStyle.completeVisitBtn}
                  icon={<Check size={"$1"} />}
                  onPress={submitDetails}
                >
                  {callEnded ? "Complete Visit" : "End Call & Complete Visit"}
                </Button>
              )}
            </YStack>
          </YStack>
        </ScrollView>
      </YStack>
      <Modal visible={dialogOpen} transparent animationType="fade">
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            width: "100%",
            height: "100%",
          }}
        >
          <DialogBox
            open={dialogOpen}
            onClose={setDialogOpen}
            title="Order Required"
            body="Before completing this visit, please enter the associated order details. This information is necessary to complete the visit record."
            btnText="Enter Order Details"
            onFinishLater={() => setDialogOpen(false)}
          />
        </View>
      </Modal>
    </View>
  );
}
