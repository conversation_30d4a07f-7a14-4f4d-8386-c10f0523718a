import { useEffect, createContext, useContext, useState } from "react";
import { router, Stack, useRouter } from "expo-router";
import {
  SafeAreaView,
  StatusBar,
  useColorScheme,
  ActivityIndicator,
  View,
  Platform,
  NativeModules,
} from "react-native";
import { TamaguiProvider } from "tamagui";
import tamaguiConfig from "tamagui.config";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider, useAuth } from "../context/AuthContext";
import { layoutStyles } from "./styles/LayoutStyle";
import { ZoomVideoSdkProvider } from "@zoom/react-native-videosdk";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ConsultationRequestProvider } from "~/context/ConsultationRequestContext";
import UpdateProvider from "src/hooks/UpdateProvider";
import { UpdateModal } from "../components/UpdateModal";
import R<PERSON>all<PERSON>eep, { IOptions } from "react-native-callkeep";
import { useUrlSchemeAuth } from "../hooks/useUrlSchemeAuth";
import { requestAndUpdatePermissions } from "lib";

const options: IOptions = {
  ios: {
    appName: "VitalCare",
    supportsVideo: true,
    maximumCallGroups: "1",
    maximumCallsPerCallGroup: "1",
  },
  android: {
    alertTitle: "Permissions required",
    alertDescription: "This app needs to access your call accounts",
    cancelButton: "Cancel",
    okButton: "OK",
    additionalPermissions: [],
  },
};

// Define theme type
type Theme = "system" | "light" | "dark";

// Define ThemeContext type
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

// Create Theme Context with default value
const ThemeContext = createContext<ThemeContextType>({
  theme: "light",
  setTheme: () => {},
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    },
  },
});

function RootContent() {
  const { user, loading } = useAuth();
  const router = useRouter();

  // Use the URL scheme authentication hook
  useUrlSchemeAuth();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        console.log("redirecting to login page as no user is found");
        router.replace("/");
      } else if (user.role === "nurse") {
        router.replace("/nurse/dashboard");
      } else if (user.role === "provider") {
        router.replace("/provider/dashboard");
      } else if (user.role === "admin") {
        // Use a valid route for admin
        router.replace("/admin/dashboard");
      }
    }
  }, [loading, user]);

  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: "white" },
        }}
      >
        <Stack.Screen name="index" />
      </Stack>
      <UpdateModal />
    </>
  );
}

export default function RootLayout() {
  const { container } = layoutStyles;
  const systemColorScheme = useColorScheme();
  const [theme, setTheme] = useState<Theme>("light");
  const [isLoading, setIsLoading] = useState(true);

  // Request permissions once when the app starts
  useEffect(() => {
    requestAndUpdatePermissions();
  }, []);

  // Set up CallKit when the app launches
  useEffect(() => {
    if (Platform.OS === "ios") {
      // Set up CallKit only once when the app starts
      RNCallKeep.setup(options);
      RNCallKeep.setAvailable(true);

      console.log("App launched, CallKit setup complete");

      // Wait for the app to be fully initialized before ending calls
      // This ensures the app has time to handle any incoming call data
      setTimeout(() => {
        console.log(
          "App should be fully initialized now, checking for active calls"
        );

        // Get active calls and end them
        RNCallKeep.getCalls()
          .then((calls: any) => {
            if (calls && Array.isArray(calls) && calls.length > 0) {
              console.log(`Found ${calls.length} active calls to end`);

              // End each call individually
              calls.forEach((call: any) => {
                if (call && call.callUUID) {
                  console.log(`Ending call with UUID: ${call.callUUID}`);
                  RNCallKeep.endCall(call.callUUID);
                }
              });
            } else {
              console.log("No active calls found to end on app initialization");
            }
          })
          .catch((error: any) => {
            console.error("Error getting calls:", error);
          });
      }, 3000); // Wait 3 seconds to ensure app is fully initialized

      // Clean up when app is closed
      return () => {
        // End any active calls when the app is closed
        try {
          const { RingtoneModule } = NativeModules;
          if (RingtoneModule && RingtoneModule.endCurrentCallKitCall) {
            RingtoneModule.endCurrentCallKitCall();
          }
        } catch (error) {
          console.error("Error ending CallKit calls during cleanup:", error);
        }
      };
    }
  }, []);

  // Load saved theme on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem("appTheme");
        if (savedTheme) {
          const validThemes: Theme[] = ["system", "light", "dark"];
          if (validThemes.includes(savedTheme as Theme)) {
            setTheme(savedTheme as Theme);
          }
        }
      } catch (error) {
        console.error("Error loading theme:", error);
      } finally {
        setIsLoading(false);
      }
    };
    loadTheme();
  }, []);

  // Save theme when it changes
  const handleSetTheme = async (newTheme: Theme) => {
    try {
      setTheme(newTheme);
      await AsyncStorage.setItem("appTheme", newTheme);
    } catch (error) {
      console.error("Error saving theme:", error);
    }
  };

  // Determine the effective theme based on user selection
  const effectiveTheme: "light" | "dark" =
    theme === "system" && systemColorScheme
      ? systemColorScheme
      : theme === "system"
        ? "light"
        : theme;

  const isDarkMode = effectiveTheme === "dark";

  if (isLoading) {
    return (
      <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <ZoomVideoSdkProvider
      config={{
        domain: "zoom.us",
        enableLog: true,
        // Disable CallKit integration in Zoom SDK
        enableCallKit: false,
      }}
    >
      <AuthProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeContext.Provider value={{ theme, setTheme: handleSetTheme }}>
            <UpdateProvider>
              <ConsultationRequestProvider>
                <TamaguiProvider
                  config={tamaguiConfig}
                  defaultTheme={effectiveTheme}
                >
                  <StatusBar
                    barStyle={isDarkMode ? "light-content" : "dark-content"}
                    backgroundColor={isDarkMode ? "#000000" : "#FFFFFF"}
                  />
                  <SafeAreaView
                    style={[
                      container,
                      { backgroundColor: isDarkMode ? "#000000" : "#FFFFFF" },
                    ]}
                  >
                    <RootContent />
                  </SafeAreaView>
                </TamaguiProvider>
              </ConsultationRequestProvider>
            </UpdateProvider>
          </ThemeContext.Provider>
        </QueryClientProvider>
      </AuthProvider>
    </ZoomVideoSdkProvider>
  );
}

export function useTheme(): ThemeContextType {
  return useContext(ThemeContext);
}
